# Weekend/Holiday Chart Data Fix

## Problem Description
Charts were not showing data when:
- Today is a weekend or holiday
- The calculated `fromDate` or `toDate` falls on a weekend or holiday

This happened because the `periodRangeCalculator` function was calculating date ranges without considering weekends and holidays, leading to API requests with invalid trading dates.

## Root Cause Analysis
The issue was in the `periodRangeCalculator` function across all chart components:

1. **LightViewCandle/utils.js**
2. **LightViewCharts/utils.js** 
3. **MultiLinePriceChart/utils.js**

The function was calculating date ranges using raw date arithmetic without adjusting for:
- Weekends (Saturday/Sunday)
- Market holidays
- Muhurat trading days (special trading sessions)

## Solution Implemented

### Changes Made
Updated the `periodRangeCalculator` function in all three chart utility files to include weekend/holiday adjustments:

```javascript
const periodRangeCalculator = (period) => {
  const currentDate = dayjs();
  const multiplier = PERIOD_CONSTANTS.firstValueInRangeDays[period]
    ? PERIOD_CONSTANTS.firstValueInRangeDays[period] - 1
    : PERIOD_CONSTANTS.firstValueInRangeDays[period];

  const previousDate = currentDate.subtract(multiplier, 'day');

  // Adjust dates for weekends and holidays
  const adjustedCurrentDate = weekendOrHoliday(currentDate);
  const adjustedPreviousDate = weekendOrHoliday(previousDate);

  return {
    startDate: adjustedCurrentDate.format('YYYY-MM-DD'),
    endDate: adjustedPreviousDate.format('YYYY-MM-DD'),
  };
};
```

### How the Fix Works
1. **Weekend Detection**: Uses `isWeekend()` to identify Saturday (6) and Sunday (0)
2. **Holiday Detection**: Checks against `globalHolidaysMap` for market holidays
3. **Muhurat Handling**: Special handling for Muhurat trading sessions via `globalMuhuratMap`
4. **Date Adjustment**: The `weekendOrHoliday()` function moves dates backward to the last valid trading day

### Files Modified
- `src/components/organisms/LightViewCandle/utils.js` (lines 153-169)
- `src/components/organisms/LightViewCharts/utils.js` (lines 153-169)
- `src/components/organisms/MultiLinePriceChart/utils.js` (lines 153-169)

## Impact
- **Before**: Charts showed no data when date calculations fell on weekends/holidays
- **After**: Charts automatically adjust to use the nearest valid trading day
- **Affected Ranges**: All time periods (1w, 1m, 3m, 1y, 3y, 5y) except 1d (which already had proper handling)

## Testing
The fix ensures that:
1. Weekend dates are adjusted to the previous Friday
2. Holiday dates are adjusted to the previous trading day
3. Muhurat trading days are preserved as valid trading days
4. All chart components (Candle, Area, Line) behave consistently

## Backward Compatibility
- No breaking changes to existing APIs
- Existing chart functionality remains unchanged
- Only improves date handling for edge cases

## Future Considerations
- The fix relies on `globalHolidaysMap` and `globalMuhuratMap` being properly populated
- Consider adding validation to ensure these maps are available before chart rendering
- Monitor for any performance impact of additional date calculations
